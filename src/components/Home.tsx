import React from 'react';
import { motion } from 'framer-motion';
import { Play, CheckCircle, Clock, Globe, Share2, Users, Award, TrendingUp, BookOpen, Star, Mail, HelpCircle } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import { LanguageSwitcher } from './LanguageSwitcher';
import { TestimonialCarousel } from './TestimonialCarousel';
import { FAQSection } from './FAQSection';
import { BlogPreview } from './BlogPreview';
import { StatsCounter } from './StatsCounter';
import { StructuredData } from './StructuredData';
import { SEOHelmet } from './SEOHelmet';
import { SearchTrigger } from './SearchModal';

export const Home: React.FC = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();

  const handleStartAssessment = () => {
    navigate('/assessment');
  };



  const features = [
    {
      icon: CheckCircle,
      key: 'comprehensive',
      color: 'text-indigo-600'
    },
    {
      icon: Clock,
      key: 'instant',
      color: 'text-emerald-600'
    },
    {
      icon: Globe,
      key: 'multilingual',
      color: 'text-amber-600'
    },
    {
      icon: Share2,
      key: 'shareable',
      color: 'text-purple-600'
    }
  ];

  const benefits = [
    {
      icon: Users,
      titleKey: 'teamDynamics',
      descKey: 'teamDynamicsDesc',
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: Award,
      titleKey: 'careerDevelopment',
      descKey: 'careerDevelopmentDesc',
      color: 'from-green-500 to-green-600'
    },
    {
      icon: TrendingUp,
      titleKey: 'leadershipSkills',
      descKey: 'leadershipSkillsDesc',
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: BookOpen,
      titleKey: 'selfAwareness',
      descKey: 'selfAwarenessDesc',
      color: 'from-orange-500 to-orange-600'
    }
  ];

  return (
    <>
      <SEOHelmet
        title={t('home.seo.title')}
        description={t('home.seo.description')}
        keywords={t('home.seo.keywords')}
        structuredDataType="website"
      />
      <StructuredData type="organization" />
      <StructuredData type="service" />

      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* Header */}
      <header className="relative z-10 py-6 bg-white/80 backdrop-blur-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 flex justify-between items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-3"
          >
            <div className="w-10 h-10 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-lg">D</span>
            </div>
            <div>
              <div className="text-2xl font-bold text-indigo-600">DISC Traits</div>
              <div className="text-xs text-gray-500">{t('home.tagline')}</div>
            </div>
          </motion.div>
          
          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link
              to="/about"
              className="text-gray-600 hover:text-indigo-600 transition-colors"
            >
              {t('navigation.about')}
            </Link>
            <Link
              to="/blog"
              className="text-gray-600 hover:text-indigo-600 transition-colors"
            >
              {t('navigation.blog')}
            </Link>
            <Link
              to="/faq"
              className="text-gray-600 hover:text-indigo-600 transition-colors"
            >
              {t('navigation.faq')}
            </Link>
            <Link
              to="/contact"
              className="text-gray-600 hover:text-indigo-600 transition-colors"
            >
              {t('navigation.contact')}
            </Link>
          </nav>

          <div className="flex items-center space-x-4">
            <SearchTrigger />
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          {/* Hero Content */}
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-4xl md:text-6xl font-bold text-gray-800 mb-6 leading-tight">
                {t('home.title')}
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed max-w-4xl mx-auto">
                {t('home.subtitle')}
              </p>
              <p className="text-lg text-gray-700 mb-12 max-w-3xl mx-auto leading-relaxed">
                {t('home.description')}
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12"
            >
              <button
                onClick={handleStartAssessment}
                className="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-xl font-semibold rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <Play className="w-6 h-6" />
                <span>{t('common.start')}</span>
              </button>
              
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Star className="w-4 h-4 text-yellow-500" />
                <span>{t('home.cta.features')}</span>
              </div>
            </motion.div>

            {/* Stats Counter */}
            <StatsCounter />
          </div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20"
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.key}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg p-6 text-center hover:shadow-xl transition-all duration-300 border border-gray-100"
              >
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 ${feature.color} mb-4`}>
                  <feature.icon className="w-6 h-6" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  {t(`home.features.${feature.key}`)}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {t(`home.features.${feature.key}Desc`)}
                </p>
              </motion.div>
            ))}
          </motion.div>

          {/* Benefits Section */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.8 }}
            className="mb-20"
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                {t('home.benefits.title')}
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                {t('home.benefits.subtitle')}
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 gap-8">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={benefit.titleKey}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 1 + index * 0.1 }}
                  className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 border border-gray-100"
                >
                  <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br ${benefit.color} text-white rounded-2xl mb-6`}>
                    <benefit.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-3">
                    {t(`home.benefits.${benefit.titleKey}`)}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {t(`home.benefits.${benefit.descKey}`)}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* DISC Types Preview */}
          <motion.div
            id="about"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1.2 }}
            className="mb-20"
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                {t('home.discTypes.title')}
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                {t('home.discTypes.subtitle')}
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {(['D', 'I', 'S', 'C'] as const).map((type, index) => {
                const colors = {
                  D: { gradient: 'from-red-500 to-red-600', bg: 'bg-red-50', text: 'text-red-700' },
                  I: { gradient: 'from-yellow-500 to-yellow-600', bg: 'bg-yellow-50', text: 'text-yellow-700' },
                  S: { gradient: 'from-green-500 to-green-600', bg: 'bg-green-50', text: 'text-green-700' },
                  C: { gradient: 'from-blue-500 to-blue-600', bg: 'bg-blue-50', text: 'text-blue-700' }
                };

                return (
                  <motion.div
                    key={type}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 1.4 + index * 0.1 }}
                    className={`${colors[type].bg} rounded-2xl shadow-lg p-6 text-center hover:shadow-xl transition-all duration-300 border border-gray-100`}
                  >
                    <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br ${colors[type].gradient} text-white rounded-full text-2xl font-bold mb-4`}>
                      {type}
                    </div>
                    <h3 className={`text-xl font-semibold ${colors[type].text} mb-2`}>
                      {t(`types.${type}.name`)}
                    </h3>
                    <p className="text-gray-600 text-sm leading-relaxed mb-4">
                      {t(`types.${type}.shortDesc`)}
                    </p>
                    <div className="text-xs text-gray-500">
                      {t('home.discTypes.learnMore')}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Testimonials */}
          <TestimonialCarousel />

          {/* Blog Preview */}
          <div id="blog">
            <BlogPreview />
          </div>

          {/* FAQ Section */}
          <div id="faq">
            <FAQSection />
          </div>

          {/* Contact Section */}
          <motion.div
            id="contact"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 2.2 }}
            className="mb-20"
          >
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                <Mail className="w-8 h-8 text-indigo-600" />
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                {t('contact.title')}
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                {t('contact.subtitle')}
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="bg-white rounded-2xl shadow-lg p-6 text-center">
                <Mail className="w-8 h-8 text-indigo-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  {t('contact.email.title')}
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  {t('contact.email.description')}
                </p>
                <a 
                  href="mailto:<EMAIL>"
                  className="text-indigo-600 hover:text-indigo-700 font-medium"
                >
                  <EMAIL>
                </a>
              </div>

              <div className="bg-white rounded-2xl shadow-lg p-6 text-center">
                <HelpCircle className="w-8 h-8 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  {t('contact.support.title')}
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  {t('contact.support.description')}
                </p>
                <Link
                  to="/faq"
                  className="text-green-600 hover:text-green-700 font-medium"
                >
                  {t('contact.support.action')}
                </Link>
              </div>

              <div className="bg-white rounded-2xl shadow-lg p-6 text-center">
                <Users className="w-8 h-8 text-purple-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  {t('contact.enterprise.title')}
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  {t('contact.enterprise.description')}
                </p>
                <a 
                  href="mailto:<EMAIL>"
                  className="text-purple-600 hover:text-purple-700 font-medium"
                >
                  {t('contact.enterprise.action')}
                </a>
              </div>
            </div>
          </motion.div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.8 }}
            className="text-center bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl p-12 text-white mb-20"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              {t('home.cta.title')}
            </h2>
            <p className="text-xl mb-8 opacity-90">
              {t('home.cta.subtitle')}
            </p>
            <button
              onClick={handleStartAssessment}
              className="inline-flex items-center space-x-3 px-8 py-4 bg-white text-indigo-600 text-xl font-semibold rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
            >
              <Play className="w-6 h-6" />
              <span>{t('home.cta.button')}</span>
            </button>
          </motion.div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">D</span>
                </div>
                <span className="text-xl font-bold">DISC Traits</span>
              </div>
              <p className="text-gray-400 text-sm leading-relaxed">
                {t('footer.description')}
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">{t('footer.links.assessment')}</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li>
                  <button
                    onClick={handleStartAssessment}
                    className="hover:text-white transition-colors"
                  >
                    {t('navigation.assessment')}
                  </button>
                </li>
                <li>
                  <Link
                    to="/about"
                    className="hover:text-white transition-colors"
                  >
                    {t('footer.sampleReport')}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/contact"
                    className="hover:text-white transition-colors"
                  >
                    {t('footer.pricing')}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/contact"
                    className="hover:text-white transition-colors"
                  >
                    {t('footer.teamAssessments')}
                  </Link>
                </li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">{t('footer.links.resources')}</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li>
                  <Link
                    to="/about"
                    className="hover:text-white transition-colors"
                  >
                    {t('footer.discGuide')}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/blog"
                    className="hover:text-white transition-colors"
                  >
                    {t('navigation.blog')}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/blog"
                    className="hover:text-white transition-colors"
                  >
                    {t('footer.caseStudies')}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/faq"
                    className="hover:text-white transition-colors"
                  >
                    {t('navigation.faq')}
                  </Link>
                </li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">{t('footer.links.support')}</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li>
                  <Link
                    to="/contact"
                    className="hover:text-white transition-colors"
                  >
                    {t('navigation.contact')}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/privacy"
                    className="hover:text-white transition-colors"
                  >
                    {t('footer.privacyPolicy')}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/terms"
                    className="hover:text-white transition-colors"
                  >
                    {t('footer.termsOfService')}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/contact"
                    className="hover:text-white transition-colors"
                  >
                    {t('footer.accessibility')}
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              {t('footer.copyright')}
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <span className="sr-only">Facebook</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clipRule="evenodd" />
                </svg>
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <span className="sr-only">Twitter</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <span className="sr-only">LinkedIn</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
    </>
  );
};