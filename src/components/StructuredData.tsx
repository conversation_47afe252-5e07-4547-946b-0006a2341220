import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../contexts/LanguageContext';

interface StructuredDataProps {
  type: 'website' | 'article' | 'faq' | 'organization' | 'service';
  data?: any;
}

export const StructuredData: React.FC<StructuredDataProps> = ({ type, data = {} }) => {
  const { language } = useLanguage();

  const getBaseUrl = () => {
    return typeof window !== 'undefined' ? window.location.origin : 'https://disc-traits.com';
  };

  const generateStructuredData = () => {
    const baseUrl = getBaseUrl();
    
    switch (type) {
      case 'website':
        return {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "DISC Traits",
          "alternateName": "DISC Personality Assessment",
          "url": baseUrl,
          "description": "Free DISC personality assessment tool to understand your behavioral style and improve communication, teamwork, and leadership skills.",
          "inLanguage": language,
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": `${baseUrl}/search?q={search_term_string}`
            },
            "query-input": "required name=search_term_string"
          },
          "publisher": {
            "@type": "Organization",
            "name": "DISC Traits",
            "url": baseUrl,
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/logo.png`,
              "width": 512,
              "height": 512
            }
          }
        };

      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "DISC Traits",
          "url": baseUrl,
          "logo": `${baseUrl}/logo.png`,
          "description": "Leading provider of DISC personality assessments for individuals and organizations worldwide.",
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+86-************",
            "contactType": "customer service",
            "email": "<EMAIL>",
            "availableLanguage": ["English", "Chinese", "Spanish"]
          },
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "1 Jianguomenwai Street, China World Trade Center Tower A, Suite 2501",
            "addressLocality": "Beijing",
            "addressRegion": "Beijing",
            "postalCode": "100020",
            "addressCountry": "CN"
          },
          "sameAs": [
            "https://www.linkedin.com/company/disc-traits",
            "https://twitter.com/disctraits",
            "https://www.facebook.com/disctraits"
          ]
        };

      case 'service':
        return {
          "@context": "https://schema.org",
          "@type": "Service",
          "name": "DISC Personality Assessment",
          "description": "Comprehensive DISC personality assessment to understand behavioral styles, improve communication, and enhance team collaboration.",
          "provider": {
            "@type": "Organization",
            "name": "DISC Traits",
            "url": baseUrl
          },
          "serviceType": "Personality Assessment",
          "areaServed": "Worldwide",
          "availableLanguage": ["English", "Chinese", "Spanish"],
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock",
            "validFrom": "2025-01-01"
          },
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "reviewCount": "15000",
            "bestRating": "5",
            "worstRating": "1"
          }
        };

      case 'article':
        return {
          "@context": "https://schema.org",
          "@type": "Article",
          "headline": data.title || "DISC Personality Assessment Article",
          "description": data.description || "Learn about DISC personality types and behavioral assessment.",
          "author": {
            "@type": "Person",
            "name": data.author || "DISC Traits Team"
          },
          "publisher": {
            "@type": "Organization",
            "name": "DISC Traits",
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/logo.png`
            }
          },
          "datePublished": data.publishedDate || new Date().toISOString(),
          "dateModified": data.modifiedDate || new Date().toISOString(),
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": data.url || baseUrl
          },
          "image": data.image || `${baseUrl}/og-image.png`,
          "articleSection": data.category || "Personality Assessment",
          "inLanguage": language
        };

      case 'faq':
        return {
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": data.faqs?.map((faq: any) => ({
            "@type": "Question",
            "name": faq.question,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": faq.answer
            }
          })) || []
        };

      default:
        return null;
    }
  };

  const structuredData = generateStructuredData();

  if (!structuredData) return null;

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
    </Helmet>
  );
};

// Breadcrumb component for better navigation and SEO
interface BreadcrumbProps {
  items: Array<{
    name: string;
    url?: string;
  }>;
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({ items }) => {
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://disc-traits.com';

  const breadcrumbStructuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url ? `${baseUrl}${item.url}` : undefined
    }))
  };

  return (
    <>
      <Helmet>
        <script type="application/ld+json">
          {JSON.stringify(breadcrumbStructuredData)}
        </script>
      </Helmet>
      <nav aria-label="Breadcrumb" className="mb-4">
        <ol className="flex items-center space-x-2 text-sm text-gray-600">
          {items.map((item, index) => (
            <li key={index} className="flex items-center">
              {index > 0 && <span className="mx-2">/</span>}
              {item.url ? (
                <a href={item.url} className="hover:text-indigo-600 transition-colors">
                  {item.name}
                </a>
              ) : (
                <span className="text-gray-900 font-medium">{item.name}</span>
              )}
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
};

// Hreflang component for multilingual SEO
export const HrefLang: React.FC = () => {
  const { language } = useLanguage();
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://disc-traits.com';
  const currentPath = typeof window !== 'undefined' ? window.location.pathname : '/';

  const languages = [
    { code: 'en', region: 'US' },
    { code: 'zh', region: 'CN' },
    { code: 'es', region: 'ES' }
  ];

  return (
    <Helmet>
      {languages.map(lang => (
        <link
          key={lang.code}
          rel="alternate"
          hrefLang={`${lang.code}-${lang.region}`}
          href={`${baseUrl}${currentPath}?lang=${lang.code}`}
        />
      ))}
      <link
        rel="alternate"
        hrefLang="x-default"
        href={`${baseUrl}${currentPath}`}
      />
    </Helmet>
  );
};
