import React, { Component, ErrorInfo, ReactNode } from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle, RefreshCw, Home, Bug, Mail } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    this.setState({
      error,
      errorInfo
    });

    // 发送错误报告
    this.reportError(error, errorInfo);
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorId: this.state.errorId
    };

    // 发送到Google Analytics
    if (typeof window.gtag !== 'undefined') {
      window.gtag('event', 'exception', {
        description: error.message,
        fatal: true,
        error_id: this.state.errorId
      });
    }

    // 发送到控制台（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.error('Error Boundary caught an error:', errorReport);
    }

    // 这里可以发送到错误监控服务
    // sendToErrorMonitoring(errorReport);
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private handleReportBug = () => {
    const subject = encodeURIComponent(`Bug Report - ${this.state.errorId}`);
    const body = encodeURIComponent(`
Error ID: ${this.state.errorId}
Error Message: ${this.state.error?.message}
URL: ${window.location.href}
Time: ${new Date().toISOString()}
User Agent: ${navigator.userAgent}

Please describe what you were doing when this error occurred:
[Your description here]
    `);
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  render() {
    if (this.state.hasError) {
      // 自定义错误页面
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-md w-full bg-white rounded-xl shadow-2xl p-8 text-center"
          >
            {/* 错误图标 */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2 }}
              className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </motion.div>

            {/* 错误标题 */}
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              哎呀，出错了！
            </h1>

            {/* 错误描述 */}
            <p className="text-gray-600 mb-6">
              很抱歉，应用遇到了一个意外错误。我们已经记录了这个问题，并会尽快修复。
            </p>

            {/* 错误ID */}
            <div className="bg-gray-50 rounded-lg p-3 mb-6">
              <p className="text-xs text-gray-500 mb-1">错误ID</p>
              <p className="text-sm font-mono text-gray-700">{this.state.errorId}</p>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-3">
              <button
                onClick={this.handleRetry}
                className="w-full flex items-center justify-center px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                重试
              </button>

              <button
                onClick={this.handleGoHome}
                className="w-full flex items-center justify-center px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <Home className="w-4 h-4 mr-2" />
                返回首页
              </button>

              <button
                onClick={this.handleReportBug}
                className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Bug className="w-4 h-4 mr-2" />
                报告问题
              </button>
            </div>

            {/* 开发环境下显示详细错误信息 */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-6 text-left">
                <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
                  开发者信息
                </summary>
                <div className="bg-gray-100 rounded-lg p-4 text-xs">
                  <div className="mb-4">
                    <h4 className="font-semibold text-red-600 mb-2">错误信息:</h4>
                    <pre className="whitespace-pre-wrap text-gray-800">
                      {this.state.error.message}
                    </pre>
                  </div>
                  
                  {this.state.error.stack && (
                    <div className="mb-4">
                      <h4 className="font-semibold text-red-600 mb-2">堆栈跟踪:</h4>
                      <pre className="whitespace-pre-wrap text-gray-600 text-xs overflow-x-auto">
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}
                  
                  {this.state.errorInfo?.componentStack && (
                    <div>
                      <h4 className="font-semibold text-red-600 mb-2">组件堆栈:</h4>
                      <pre className="whitespace-pre-wrap text-gray-600 text-xs overflow-x-auto">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}

            {/* 联系信息 */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-xs text-gray-500">
                如果问题持续存在，请联系我们：
              </p>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center text-xs text-indigo-600 hover:text-indigo-700 mt-1"
              >
                <Mail className="w-3 h-3 mr-1" />
                <EMAIL>
              </a>
            </div>
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}

// 高阶组件，用于包装其他组件
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// 错误报告工具
export class ErrorReporter {
  private static errorQueue: any[] = [];
  private static isOnline = navigator.onLine;

  static {
    // 监听网络状态
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushErrorQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // 监听未捕获的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError(new Error(event.reason), 'unhandledrejection');
    });

    // 监听全局错误
    window.addEventListener('error', (event) => {
      this.reportError(event.error || new Error(event.message), 'global');
    });
  }

  static reportError(error: Error, context: string = 'unknown') {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: localStorage.getItem('user-id') || 'anonymous'
    };

    if (this.isOnline) {
      this.sendErrorReport(errorReport);
    } else {
      this.errorQueue.push(errorReport);
    }
  }

  private static sendErrorReport(errorReport: any) {
    // 这里可以发送到错误监控服务
    console.error('Error Report:', errorReport);
    
    // 发送到Google Analytics
    if (typeof window.gtag !== 'undefined') {
      window.gtag('event', 'exception', {
        description: errorReport.message,
        fatal: false
      });
    }
  }

  private static flushErrorQueue() {
    while (this.errorQueue.length > 0) {
      const errorReport = this.errorQueue.shift();
      this.sendErrorReport(errorReport);
    }
  }
}

export default ErrorBoundary;
