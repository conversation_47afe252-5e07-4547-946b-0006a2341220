import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

// Google Analytics 4 配置
const GA_MEASUREMENT_ID = 'G-XXXXXXXXXX'; // 替换为实际的测量ID

// 声明gtag函数类型
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js',
      targetId: string | Date,
      config?: Record<string, any>
    ) => void;
    dataLayer: any[];
  }
}

// 初始化Google Analytics
export const initGA = () => {
  // 只在生产环境中加载GA
  if (process.env.NODE_ENV !== 'production') {
    return;
  }

  // 创建gtag脚本
  const script1 = document.createElement('script');
  script1.async = true;
  script1.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
  document.head.appendChild(script1);

  // 初始化dataLayer和gtag函数
  window.dataLayer = window.dataLayer || [];
  window.gtag = function gtag() {
    window.dataLayer.push(arguments);
  };

  // 配置GA
  window.gtag('js', new Date());
  window.gtag('config', GA_MEASUREMENT_ID, {
    page_title: document.title,
    page_location: window.location.href,
    send_page_view: true,
    // 隐私设置
    anonymize_ip: true,
    allow_google_signals: false,
    allow_ad_personalization_signals: false
  });
};

// 页面浏览事件
export const trackPageView = (path: string, title?: string) => {
  if (typeof window.gtag !== 'undefined') {
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_path: path,
      page_title: title || document.title,
      page_location: window.location.href
    });
  }
};

// 自定义事件追踪
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number
) => {
  if (typeof window.gtag !== 'undefined') {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value
    });
  }
};

// DISC评估相关事件
export const trackAssessmentEvent = {
  start: () => trackEvent('start_assessment', 'assessment', 'disc_assessment'),
  complete: (result: string) => trackEvent('complete_assessment', 'assessment', result),
  retake: () => trackEvent('retake_assessment', 'assessment', 'disc_assessment'),
  downloadPDF: () => trackEvent('download_pdf', 'assessment', 'disc_report'),
  shareResult: (platform: string) => trackEvent('share_result', 'social', platform)
};

// 博客相关事件
export const trackBlogEvent = {
  readArticle: (articleId: string) => trackEvent('read_article', 'blog', articleId),
  shareArticle: (articleId: string, platform: string) => 
    trackEvent('share_article', 'blog', `${articleId}_${platform}`),
  subscribe: () => trackEvent('newsletter_subscribe', 'engagement', 'blog_newsletter')
};

// 联系表单事件
export const trackContactEvent = {
  submitForm: (type: string) => trackEvent('submit_contact_form', 'contact', type),
  clickEmail: () => trackEvent('click_email', 'contact', 'email_link'),
  clickPhone: () => trackEvent('click_phone', 'contact', 'phone_link')
};

// 语言切换事件
export const trackLanguageChange = (language: string) => {
  trackEvent('change_language', 'user_preference', language);
};

// Google Analytics Hook组件
export const GoogleAnalytics: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    // 初始化GA（仅在首次加载时）
    initGA();
  }, []);

  useEffect(() => {
    // 追踪页面浏览
    trackPageView(location.pathname + location.search);
  }, [location]);

  return null;
};

// 转化目标追踪
export const trackConversion = (goalId: string, value?: number) => {
  if (typeof window.gtag !== 'undefined') {
    window.gtag('event', 'conversion', {
      send_to: `${GA_MEASUREMENT_ID}/${goalId}`,
      value: value,
      currency: 'USD'
    });
  }
};

// 用户参与度追踪
export const trackEngagement = {
  timeOnPage: (seconds: number) => trackEvent('time_on_page', 'engagement', 'seconds', seconds),
  scrollDepth: (percentage: number) => trackEvent('scroll_depth', 'engagement', 'percentage', percentage),
  clickCTA: (ctaName: string) => trackEvent('click_cta', 'engagement', ctaName)
};

// 错误追踪
export const trackError = (error: string, page: string) => {
  trackEvent('javascript_error', 'error', `${page}: ${error}`);
};

export default GoogleAnalytics;
