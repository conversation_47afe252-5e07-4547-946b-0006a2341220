import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, ChevronDown, Search, HelpCircle } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { SEOHelmet } from '../components/SEOHelmet';
import { Link } from 'react-router-dom';

export const FAQPage: React.FC = () => {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [openItems, setOpenItems] = useState<number[]>([]);

  const faqCategories = [
    {
      title: '关于DISC评估',
      items: [
        {
          question: '什么是DISC人格评估？',
          answer: 'DISC是一种基于心理学的行为评估工具，用于了解个人的行为风格和沟通偏好。它将人格分为四种主要类型：支配型(D)、影响型(I)、稳定型(S)和谨慎型(C)。'
        },
        {
          question: '评估需要多长时间？',
          answer: '我们的DISC评估包含24个问题，通常需要5-10分钟完成。我们建议您在安静的环境中一次性完成，以确保结果的准确性。'
        },
        {
          question: '评估是否经过科学验证？',
          answer: '是的，我们的DISC评估基于威廉·马斯顿博士的原始理论，并经过现代心理学研究的验证和改进。评估具有良好的信度和效度。'
        },
        {
          question: '评估结果有多准确？',
          answer: '我们的评估准确率超过90%。结果的准确性很大程度上取决于您回答问题时的诚实程度。我们建议您选择最自然、最直观的答案。'
        }
      ]
    },
    {
      title: '使用和结果',
      items: [
        {
          question: '我能从结果中学到什么？',
          answer: '您将了解自己的主要行为风格、沟通偏好、优势和发展领域。结果还包括职场建议、团队协作技巧和个人发展建议。'
        },
        {
          question: '我可以重新进行评估吗？',
          answer: '可以的。我们建议每6-12个月重新评估一次，因为个人的行为风格可能会随着经历和环境的变化而有所调整。'
        },
        {
          question: '如何分享我的结果？',
          answer: '您可以下载PDF报告，或使用我们的分享功能在社交媒体上分享您的结果摘要。您也可以将结果链接发送给同事或朋友。'
        },
        {
          question: '结果会保存多长时间？',
          answer: '您的评估结果会永久保存在本地浏览器中。我们建议您下载PDF报告作为备份，以防浏览器数据丢失。'
        }
      ]
    },
    {
      title: '隐私和安全',
      items: [
        {
          question: '我的数据安全和隐私如何保障？',
          answer: '我们非常重视您的隐私。所有评估数据都存储在您的本地设备上，我们不会收集或存储您的个人信息。您的答案和结果完全私密。'
        },
        {
          question: '你们会收集我的个人信息吗？',
          answer: '不会。我们的评估完全匿名，不需要注册或提供个人信息。我们只收集匿名的使用统计数据来改进服务。'
        },
        {
          question: '评估过程中的安全措施是什么？',
          answer: '我们实施了多项安全措施来确保评估的完整性，包括防止复制粘贴、监控异常行为等。这些措施旨在确保结果的准确性。'
        }
      ]
    },
    {
      title: '团队和企业',
      items: [
        {
          question: '团队可以使用这个评估吗？',
          answer: '当然可以！DISC评估非常适合团队建设。我们提供企业解决方案，包括团队报告、管理仪表板和定制化分析。'
        },
        {
          question: '如何为我的团队获取评估？',
          answer: '请联系我们的企业销售团队(<EMAIL>)，我们将为您提供团队评估方案和批量许可。'
        },
        {
          question: '是否提供团队培训？',
          answer: '是的，我们提供基于DISC的团队培训和工作坊，帮助团队更好地理解和应用DISC洞察来改善协作。'
        }
      ]
    },
    {
      title: '技术支持',
      items: [
        {
          question: '评估在移动设备上可以使用吗？',
          answer: '是的，我们的评估完全响应式设计，在手机、平板和电脑上都能完美运行。我们建议使用较大的屏幕以获得最佳体验。'
        },
        {
          question: '支持哪些浏览器？',
          answer: '我们支持所有现代浏览器，包括Chrome、Firefox、Safari和Edge。建议使用最新版本以获得最佳性能。'
        },
        {
          question: '如果遇到技术问题怎么办？',
          answer: '请联系我们的技术支持团队(<EMAIL>)，或查看我们的帮助文档。我们通常在24小时内回复技术问题。'
        }
      ]
    }
  ];

  const allItems = faqCategories.flatMap((category, categoryIndex) =>
    category.items.map((item, itemIndex) => ({
      ...item,
      categoryTitle: category.title,
      id: categoryIndex * 100 + itemIndex
    }))
  );

  const filteredItems = searchTerm
    ? allItems.filter(item =>
        item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.answer.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : allItems;

  const toggleItem = (id: number) => {
    setOpenItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <SEOHelmet
        title={t('pages.faq.title')}
        description={t('pages.faq.metaDescription')}
        keywords="DISC常见问题, 人格评估FAQ, DISC帮助, 评估指南"
        structuredDataType="faq"
        structuredData={{
          faqs: faqCategories.flatMap(category =>
            category.items.map(item => ({
              question: item.question,
              answer: item.answer
            }))
          )
        }}
      />
      
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <Link 
            to="/" 
            className="inline-flex items-center space-x-2 text-indigo-600 hover:text-indigo-700 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>{t('common.returnHome')}</span>
          </Link>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-16">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <HelpCircle className="w-8 h-8 text-indigo-600" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            {t('pages.faq.title')}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('pages.faq.subtitle')}
          </p>
        </motion.div>

        {/* Search */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-12"
        >
          <div className="relative max-w-2xl mx-auto">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="搜索常见问题..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-lg"
            />
          </div>
        </motion.div>

        {/* FAQ Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {searchTerm ? (
            // Search Results
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-800">
                  搜索结果 ({filteredItems.length})
                </h2>
              </div>
              <div className="divide-y divide-gray-200">
                {filteredItems.map((item) => (
                  <div key={item.id} className="p-6">
                    <button
                      onClick={() => toggleItem(item.id)}
                      className="w-full text-left flex items-center justify-between hover:text-indigo-600 transition-colors"
                    >
                      <div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-1">
                          {item.question}
                        </h3>
                        <p className="text-sm text-gray-500">{item.categoryTitle}</p>
                      </div>
                      <ChevronDown 
                        className={`w-5 h-5 text-gray-400 transition-transform ${
                          openItems.includes(item.id) ? 'rotate-180' : ''
                        }`}
                      />
                    </button>
                    <AnimatePresence>
                      {openItems.includes(item.id) && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="overflow-hidden"
                        >
                          <div className="pt-4 text-gray-700 leading-relaxed">
                            {item.answer}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            // Category View
            <div className="space-y-8">
              {faqCategories.map((category, categoryIndex) => (
                <div key={categoryIndex} className="bg-white rounded-2xl shadow-xl overflow-hidden">
                  <div className="p-6 bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
                    <h2 className="text-2xl font-bold">{category.title}</h2>
                  </div>
                  <div className="divide-y divide-gray-200">
                    {category.items.map((item, itemIndex) => {
                      const itemId = categoryIndex * 100 + itemIndex;
                      return (
                        <div key={itemIndex} className="p-6">
                          <button
                            onClick={() => toggleItem(itemId)}
                            className="w-full text-left flex items-center justify-between hover:text-indigo-600 transition-colors"
                          >
                            <h3 className="text-lg font-semibold text-gray-800">
                              {item.question}
                            </h3>
                            <ChevronDown 
                              className={`w-5 h-5 text-gray-400 transition-transform ${
                                openItems.includes(itemId) ? 'rotate-180' : ''
                              }`}
                            />
                          </button>
                          <AnimatePresence>
                            {openItems.includes(itemId) && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: 'auto', opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.3 }}
                                className="overflow-hidden"
                              >
                                <div className="pt-4 text-gray-700 leading-relaxed">
                                  {item.answer}
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          )}
        </motion.div>

        {/* Contact CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">还有其他问题？</h3>
            <p className="mb-6 opacity-90">如果您没有找到答案，请随时联系我们的支持团队</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to="/contact" 
                className="inline-flex items-center px-6 py-3 bg-white text-indigo-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                联系我们
              </Link>
              <a 
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-6 py-3 border-2 border-white text-white rounded-lg font-semibold hover:bg-white hover:text-indigo-600 transition-colors"
              >
                发送邮件
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};
