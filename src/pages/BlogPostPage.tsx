import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Calendar, Clock, User, Share2, BookOpen, ArrowRight } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { SEOHelmet } from '../components/SEOHelmet';
import { Link, useParams } from 'react-router-dom';

export const BlogPostPage: React.FC = () => {
  const { t } = useLanguage();
  const { slug } = useParams<{ slug: string }>();

  // 模拟博客文章数据
  const blogPosts: Record<string, any> = {
    'disc-remote-team-management': {
      title: '如何在远程团队管理中使用DISC',
      excerpt: '发现使用DISC人格洞察管理远程团队的有效策略，以改善沟通和生产力。',
      author: '莎拉·米切尔博士',
      date: '2025-01-10',
      readTime: '5分钟阅读',
      category: '团队管理',
      content: `
        <p>随着远程工作成为新常态，团队管理面临着前所未有的挑战。传统的面对面沟通方式不再适用，管理者需要新的工具和策略来维持团队凝聚力和生产力。DISC人格评估为远程团队管理提供了强有力的解决方案。</p>

        <h2>远程工作的挑战</h2>
        <p>远程工作环境中，团队成员面临以下主要挑战：</p>
        <ul>
          <li>缺乏面对面的非语言沟通</li>
          <li>时区差异导致的协作困难</li>
          <li>孤立感和团队归属感缺失</li>
          <li>工作与生活边界模糊</li>
          <li>不同工作风格的冲突</li>
        </ul>

        <h2>DISC在远程团队中的应用</h2>
        
        <h3>1. 支配型(D)团队成员</h3>
        <p><strong>特点：</strong>直接、果断、目标导向</p>
        <p><strong>远程管理策略：</strong></p>
        <ul>
          <li>提供清晰的目标和期望</li>
          <li>给予充分的自主权</li>
          <li>定期进行简短的进度检查</li>
          <li>避免过度的微观管理</li>
        </ul>

        <h3>2. 影响型(I)团队成员</h3>
        <p><strong>特点：</strong>热情、社交、乐观</p>
        <p><strong>远程管理策略：</strong></p>
        <ul>
          <li>增加视频会议和社交互动</li>
          <li>创造虚拟团队建设机会</li>
          <li>提供公开认可和赞扬</li>
          <li>鼓励创意和头脑风暴</li>
        </ul>

        <h3>3. 稳定型(S)团队成员</h3>
        <p><strong>特点：</strong>稳定、支持、团队导向</p>
        <p><strong>远程管理策略：</strong></p>
        <ul>
          <li>提供稳定的工作结构</li>
          <li>确保充分的支持和资源</li>
          <li>建立定期的一对一沟通</li>
          <li>逐步引入变化</li>
        </ul>

        <h3>4. 谨慎型(C)团队成员</h3>
        <p><strong>特点：</strong>分析、精确、质量导向</p>
        <p><strong>远程管理策略：</strong></p>
        <ul>
          <li>提供详细的工作指导</li>
          <li>确保充足的时间完成任务</li>
          <li>建立清晰的质量标准</li>
          <li>提供必要的工具和信息</li>
        </ul>

        <h2>实施建议</h2>
        
        <h3>1. 团队DISC评估</h3>
        <p>首先让所有团队成员完成DISC评估，了解每个人的行为风格。创建团队DISC概览，帮助成员理解彼此的工作方式。</p>

        <h3>2. 个性化沟通策略</h3>
        <p>根据每个成员的DISC类型调整沟通方式：</p>
        <ul>
          <li><strong>D型：</strong>简洁直接，关注结果</li>
          <li><strong>I型：</strong>热情互动，包含社交元素</li>
          <li><strong>S型：</strong>耐心支持，提供安全感</li>
          <li><strong>C型：</strong>详细准确，提供充分信息</li>
        </ul>

        <h3>3. 虚拟团队建设</h3>
        <p>设计适合不同DISC类型的团队建设活动：</p>
        <ul>
          <li>竞争性挑战（适合D型）</li>
          <li>创意协作项目（适合I型）</li>
          <li>支持性小组讨论（适合S型）</li>
          <li>知识分享会议（适合C型）</li>
        </ul>

        <h2>成功案例</h2>
        <p>TechCorp公司在实施基于DISC的远程团队管理后，团队协作效率提升了40%，员工满意度增加了35%。关键成功因素包括：</p>
        <ul>
          <li>全员DISC培训</li>
          <li>个性化管理方法</li>
          <li>定期团队动态评估</li>
          <li>持续的反馈和调整</li>
        </ul>

        <h2>结论</h2>
        <p>DISC人格评估为远程团队管理提供了科学的框架。通过理解团队成员的行为风格，管理者可以采用更有效的沟通和管理策略，提升团队凝聚力和生产力。在远程工作时代，这种个性化的管理方法将成为团队成功的关键因素。</p>
      `
    },
    'disc-science-research-validation': {
      title: 'DISC背后的科学：研究与验证',
      excerpt: '探索使DISC成为最受信任的人格评估工具之一的心理学研究和科学验证。',
      author: '迈克尔·约翰逊教授',
      date: '2025-01-08',
      readTime: '7分钟阅读',
      category: '研究',
      content: `
        <p>DISC人格评估自1928年威廉·马斯顿博士首次提出以来，经历了近一个世纪的发展和验证。本文将深入探讨DISC理论的科学基础、研究历程以及现代验证结果。</p>

        <h2>理论起源</h2>
        <p>威廉·马斯顿博士在其著作《正常人的情绪》中首次提出了DISC理论。马斯顿观察到人们在面对环境挑战时表现出四种基本的行为模式：</p>
        <ul>
          <li><strong>Dominance (支配)：</strong>在对抗性环境中的主动行为</li>
          <li><strong>Influence (影响)：</strong>在友好环境中的主动行为</li>
          <li><strong>Steadiness (稳定)：</strong>在友好环境中的被动行为</li>
          <li><strong>Conscientiousness (谨慎)：</strong>在对抗性环境中的被动行为</li>
        </ul>

        <h2>现代心理学验证</h2>
        
        <h3>大五人格模型的关联</h3>
        <p>现代心理学研究表明，DISC与广泛接受的大五人格模型存在显著关联：</p>
        <ul>
          <li><strong>D型</strong>与外向性和低宜人性相关</li>
          <li><strong>I型</strong>与高外向性和高宜人性相关</li>
          <li><strong>S型</strong>与高宜人性和高神经质稳定性相关</li>
          <li><strong>C型</strong>与高尽责性和低外向性相关</li>
        </ul>

        <h3>神经科学研究</h3>
        <p>近年来的神经科学研究为DISC理论提供了生物学基础：</p>
        <ul>
          <li><strong>前额叶皮层活动</strong>与D型的决策风格相关</li>
          <li><strong>镜像神经元系统</strong>与I型的社交能力相关</li>
          <li><strong>杏仁核反应</strong>与S型的情绪稳定性相关</li>
          <li><strong>前扣带皮层</strong>与C型的注意力控制相关</li>
        </ul>

        <h2>信度和效度研究</h2>
        
        <h3>内部一致性</h3>
        <p>多项研究表明DISC评估具有良好的内部一致性：</p>
        <ul>
          <li>Cronbach's α系数通常在0.75-0.90之间</li>
          <li>测试-重测信度在0.80-0.95之间</li>
          <li>跨文化研究显示一致的因子结构</li>
        </ul>

        <h3>预测效度</h3>
        <p>DISC评估在多个领域显示出良好的预测效度：</p>
        <ul>
          <li><strong>工作绩效：</strong>相关系数r=0.35-0.55</li>
          <li><strong>领导效能：</strong>相关系数r=0.40-0.60</li>
          <li><strong>团队协作：</strong>相关系数r=0.30-0.50</li>
          <li><strong>销售业绩：</strong>相关系数r=0.25-0.45</li>
        </ul>

        <h2>跨文化验证</h2>
        <p>DISC理论在全球范围内得到验证：</p>
        
        <h3>文化适应性研究</h3>
        <ul>
          <li><strong>西方文化：</strong>原始四因子模型得到强力支持</li>
          <li><strong>东亚文化：</strong>S型特征更为突出，但整体结构保持一致</li>
          <li><strong>拉丁文化：</strong>I型特征表现更明显</li>
          <li><strong>中东文化：</strong>D型和C型特征平衡发展</li>
        </ul>

        <h2>现代应用研究</h2>
        
        <h3>组织心理学</h3>
        <p>在组织环境中，DISC评估显示出显著价值：</p>
        <ul>
          <li>提高招聘准确性25-40%</li>
          <li>减少员工流失率15-30%</li>
          <li>改善团队绩效20-35%</li>
          <li>增强领导效能30-50%</li>
        </ul>

        <h3>教育心理学</h3>
        <p>在教育领域的应用研究表明：</p>
        <ul>
          <li>个性化教学方法提升学习效果</li>
          <li>改善师生关系和课堂管理</li>
          <li>促进学生自我认知和发展</li>
        </ul>

        <h2>技术发展与创新</h2>
        
        <h3>计算机自适应测试</h3>
        <p>现代DISC评估采用先进的心理测量技术：</p>
        <ul>
          <li>项目反应理论(IRT)优化题目选择</li>
          <li>自适应算法提高测量精度</li>
          <li>机器学习改进结果解释</li>
        </ul>

        <h3>大数据分析</h3>
        <p>大规模数据分析为DISC理论提供新的洞察：</p>
        <ul>
          <li>识别新的行为模式</li>
          <li>优化评估算法</li>
          <li>发现文化和行业特异性</li>
        </ul>

        <h2>未来研究方向</h2>
        <p>DISC理论的未来发展方向包括：</p>
        <ul>
          <li><strong>人工智能集成：</strong>利用AI技术提升评估准确性</li>
          <li><strong>实时行为分析：</strong>通过数字足迹分析行为模式</li>
          <li><strong>神经反馈：</strong>结合脑电图等技术深化理解</li>
          <li><strong>个性化发展：</strong>基于DISC的个人成长路径</li>
        </ul>

        <h2>结论</h2>
        <p>经过近一个世纪的发展和验证，DISC理论已经成为心理学和组织行为学领域最可靠的工具之一。其科学基础扎实，应用价值显著，为个人发展和组织管理提供了宝贵的洞察。随着技术的不断进步，DISC评估将继续演进，为人类行为理解贡献更多价值。</p>
      `
    }
  };

  const currentPost = slug ? blogPosts[slug] : null;

  if (!currentPost) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">{t('blog.postNotFound')}</h1>
          <Link to="/blog" className="text-indigo-600 hover:text-indigo-700">
            {t('blog.backToBlog')}
          </Link>
        </div>
      </div>
    );
  }

  const relatedPosts = Object.entries(blogPosts)
    .filter(([key]) => key !== slug)
    .slice(0, 2)
    .map(([key, post]) => ({ slug: key, ...post }));

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <SEOHelmet
        title={currentPost.title}
        description={currentPost.excerpt}
        keywords={`${currentPost.category}, DISC, 人格评估, ${currentPost.title}`}
        type="article"
        author={currentPost.author}
        publishedTime={currentPost.date}
        structuredDataType="article"
        structuredData={{
          title: currentPost.title,
          description: currentPost.excerpt,
          author: currentPost.author,
          publishedDate: currentPost.date,
          category: currentPost.category,
          url: window.location.href
        }}
      />
      
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <Link
            to="/blog"
            className="inline-flex items-center space-x-2 text-indigo-600 hover:text-indigo-700 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>{t('blog.backToBlog')}</span>
          </Link>
        </div>
      </div>

      <article className="max-w-4xl mx-auto px-4 py-16">
        {/* Article Header */}
        <motion.header
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-12"
        >
          <div className="text-center mb-8">
            <div className="inline-block bg-indigo-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
              {currentPost.category}
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6 leading-tight">
              {currentPost.title}
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              {currentPost.excerpt}
            </p>
            
            {/* Article Meta */}
            <div className="flex items-center justify-center space-x-6 text-gray-500">
              <div className="flex items-center space-x-2">
                <User className="w-5 h-5" />
                <span>{currentPost.author}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5" />
                <span>{currentPost.date}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-5 h-5" />
                <span>{currentPost.readTime}</span>
              </div>
            </div>
          </div>
        </motion.header>

        {/* Article Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-2xl shadow-xl p-8 md:p-12 mb-12"
        >
          <div 
            className="prose prose-lg max-w-none prose-indigo prose-headings:text-gray-800 prose-p:text-gray-700 prose-li:text-gray-700"
            dangerouslySetInnerHTML={{ __html: currentPost.content }}
          />
        </motion.div>

        {/* Share Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-white rounded-2xl shadow-xl p-8 mb-12"
        >
          <div className="text-center">
            <h3 className="text-xl font-bold text-gray-800 mb-4">{t('blog.share.title')}</h3>
            <div className="flex justify-center space-x-4">
              <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Share2 className="w-4 h-4" />
                <span>{t('blog.share.wechat')}</span>
              </button>
              <button className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                <Share2 className="w-4 h-4" />
                <span>{t('blog.share.weibo')}</span>
              </button>
              <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <Share2 className="w-4 h-4" />
                <span>{t('blog.share.linkedin')}</span>
              </button>
            </div>
          </div>
        </motion.div>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="bg-white rounded-2xl shadow-xl p-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
              <BookOpen className="w-6 h-6 text-indigo-600 mr-3" />
              {t('blog.relatedArticles')}
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              {relatedPosts.map((post) => (
                <Link
                  key={post.slug}
                  to={`/blog/${post.slug}`}
                  className="group border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow"
                >
                  <div className="mb-3">
                    <span className="inline-block bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium">
                      {post.category}
                    </span>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-800 mb-2 group-hover:text-indigo-600 transition-colors">
                    {post.title}
                  </h4>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{post.author}</span>
                    <div className="flex items-center space-x-1 text-indigo-600 group-hover:text-indigo-700">
                      <span>{t('blog.readMore')}</span>
                      <ArrowRight className="w-4 h-4" />
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </motion.div>
        )}
      </article>
    </div>
  );
};
