import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Shield, Eye, Lock, Database, UserCheck, AlertTriangle } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { SEOHelmet } from '../components/SEOHelmet';
import { Link } from 'react-router-dom';

export const PrivacyPage: React.FC = () => {
  const { t } = useLanguage();

  const sections = [
    {
      icon: Database,
      title: '信息收集',
      content: [
        '我们不收集任何个人身份信息。',
        '评估数据完全存储在您的本地设备上。',
        '我们只收集匿名的使用统计数据来改进服务。',
        '不需要注册或提供邮箱地址即可使用评估。'
      ]
    },
    {
      icon: Eye,
      title: '信息使用',
      content: [
        '匿名统计数据用于改进评估质量和用户体验。',
        '我们不会将任何数据出售给第三方。',
        '技术日志仅用于维护和故障排除。',
        '所有数据处理都符合GDPR和相关隐私法规。'
      ]
    },
    {
      icon: Lock,
      title: '数据安全',
      content: [
        '所有数据传输都使用SSL加密。',
        '评估结果存储在您的浏览器本地存储中。',
        '我们的服务器不存储任何评估答案或结果。',
        '定期进行安全审计和漏洞扫描。'
      ]
    },
    {
      icon: UserCheck,
      title: '您的权利',
      content: [
        '您可以随时清除本地存储的评估数据。',
        '您有权要求删除任何可能收集的数据。',
        '您可以选择不参与匿名统计数据收集。',
        '您有权了解我们如何处理您的数据。'
      ]
    },
    {
      icon: AlertTriangle,
      title: 'Cookie使用',
      content: [
        '我们使用必要的Cookie来确保网站正常运行。',
        '分析Cookie帮助我们了解网站使用情况。',
        '您可以通过浏览器设置管理Cookie偏好。',
        '禁用Cookie可能影响某些功能的使用。'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <SEOHelmet 
        title="隐私政策"
        description="了解DISC Traits如何保护您的隐私和数据安全。我们承诺不收集个人信息，所有评估数据都存储在您的本地设备上。"
        keywords="隐私政策, 数据保护, GDPR, 数据安全, 个人信息"
      />
      
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <Link 
            to="/" 
            className="inline-flex items-center space-x-2 text-indigo-600 hover:text-indigo-700 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>{t('common.returnHome')}</span>
          </Link>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-16">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Shield className="w-8 h-8 text-indigo-600" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            隐私政策
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            我们承诺保护您的隐私。本政策说明我们如何收集、使用和保护您的信息。
          </p>
          <div className="mt-4 text-sm text-gray-500">
            最后更新：2025年1月15日
          </div>
        </motion.div>

        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-2xl shadow-xl p-8 mb-8"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-4">概述</h2>
          <div className="prose max-w-none text-gray-700">
            <p className="mb-4">
              DISC Traits（"我们"、"我们的"或"本服务"）致力于保护您的隐私。本隐私政策解释了当您使用我们的DISC人格评估服务时，我们如何收集、使用、披露和保护您的信息。
            </p>
            <p className="mb-4">
              我们的核心原则是<strong>隐私优先</strong>。我们设计的评估系统不收集任何个人身份信息，所有评估数据都存储在您的本地设备上。
            </p>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-green-800 font-medium">
                ✓ 无需注册 ✓ 无个人信息收集 ✓ 本地数据存储 ✓ 完全匿名
              </p>
            </div>
          </div>
        </motion.div>

        {/* Privacy Sections */}
        <div className="space-y-6">
          {sections.map((section, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              className="bg-white rounded-2xl shadow-xl p-8"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mr-4">
                  <section.icon className="w-6 h-6 text-indigo-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800">{section.title}</h2>
              </div>
              <ul className="space-y-3">
                {section.content.map((item, itemIndex) => (
                  <li key={itemIndex} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-indigo-600 rounded-full mt-2 flex-shrink-0" />
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Third Party Services */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="bg-white rounded-2xl shadow-xl p-8 mt-8"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-6">第三方服务</h2>
          <div className="space-y-4 text-gray-700">
            <p>
              我们可能使用以下第三方服务来改进用户体验：
            </p>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-800 mb-2">Google Analytics</h3>
                <p className="text-sm text-gray-600">
                  用于匿名网站使用统计。您可以通过浏览器设置或Google的退出工具选择不参与。
                </p>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-800 mb-2">CDN服务</h3>
                <p className="text-sm text-gray-600">
                  用于提高网站加载速度。这些服务可能记录IP地址用于技术目的。
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Data Retention */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.9 }}
          className="bg-white rounded-2xl shadow-xl p-8 mt-8"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-6">数据保留</h2>
          <div className="space-y-4 text-gray-700">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">评估数据</h3>
                <p className="text-sm">
                  存储在您的浏览器本地存储中，直到您手动删除或清除浏览器数据。
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">匿名统计</h3>
                <p className="text-sm">
                  保留24个月用于服务改进，之后自动删除。
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">技术日志</h3>
                <p className="text-sm">
                  保留30天用于故障排除和安全监控。
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">联系信息</h3>
                <p className="text-sm">
                  如果您联系我们，相关信息保留3年或直到问题解决。
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Contact and Updates */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-8 text-white mt-8"
        >
          <h2 className="text-2xl font-bold mb-4">联系我们</h2>
          <p className="mb-6 opacity-90">
            如果您对本隐私政策有任何问题或需要行使您的隐私权利，请联系我们：
          </p>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2">隐私问题</h3>
              <a href="mailto:<EMAIL>" className="text-white hover:text-gray-200">
                <EMAIL>
              </a>
            </div>
            <div>
              <h3 className="font-semibold mb-2">一般支持</h3>
              <a href="mailto:<EMAIL>" className="text-white hover:text-gray-200">
                <EMAIL>
              </a>
            </div>
          </div>
          <div className="mt-6 pt-6 border-t border-white/20">
            <p className="text-sm opacity-80">
              我们可能会不时更新本隐私政策。重大变更将在网站上显著位置通知用户。
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};
