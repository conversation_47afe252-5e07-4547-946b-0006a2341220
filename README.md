# DISC Traits - 免费DISC人格评估网站

一个现代化的、响应式的DISC人格评估网站，提供科学可靠的行为风格分析。

## 🌟 功能特性

### 核心功能
- **免费DISC评估** - 24题科学评估，5分钟完成
- **详细报告** - 个性化的DISC类型分析报告
- **多语言支持** - 支持中文、英文、西班牙语
- **响应式设计** - 完美适配桌面、平板、手机
- **PWA支持** - 可安装为原生应用

### 技术特性
- **现代化架构** - React 18 + TypeScript + Vite
- **性能优化** - 代码分割、懒加载、缓存策略
- **SEO优化** - 结构化数据、多语言SEO、sitemap
- **可访问性** - WCAG 2.1 AA标准，键盘导航支持
- **安全性** - CSP、XSS防护、数据加密
- **监控分析** - Google Analytics、错误追踪、性能监控

### 用户体验
- **无障碍访问** - 高对比度、大字体、屏幕阅读器支持
- **离线功能** - Service Worker缓存，离线可用
- **搜索功能** - 全站搜索，键盘快捷键
- **反馈系统** - 用户反馈收集和处理
- **个性化** - 用户偏好设置和记忆

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 9+

### 安装依赖
```bash
npm install
```

### 开发环境
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 🧪 测试

### 运行测试
```bash
npm run test
```

### 测试覆盖率
```bash
npm run test:coverage
```

### 测试UI界面
```bash
npm run test:ui
```

### 类型检查
```bash
npm run type-check
```

## 📦 部署

### 测试环境部署
```bash
npm run deploy:staging
```

### 生产环境部署
```bash
npm run deploy:production
```

### 手动部署
```bash
# 构建
npm run build

# 部署到服务器
rsync -avz --delete dist/ user@server:/var/www/disc-traits/
```

## 🛠️ 开发指南

### 项目结构
```
src/
├── components/          # React组件
│   ├── __tests__/      # 组件测试
│   ├── Home.tsx        # 首页组件
│   ├── Assessment.tsx  # 评估组件
│   └── ...
├── pages/              # 页面组件
├── contexts/           # React Context
├── hooks/              # 自定义Hooks
├── utils/              # 工具函数
├── data/               # 数据文件
├── styles/             # 样式文件
└── types/              # TypeScript类型定义
```

### 代码规范
- **TypeScript** - 严格类型检查
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Husky** - Git钩子
- **Lint-staged** - 提交前检查

### 组件开发
```tsx
// 组件模板
import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';

interface ComponentProps {
  // 定义props类型
}

export const Component: React.FC<ComponentProps> = ({ }) => {
  const { t } = useLanguage();
  
  return (
    <div>
      {/* 组件内容 */}
    </div>
  );
};
```

### 测试编写
```tsx
// 测试模板
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Component } from '../Component';

describe('Component', () => {
  it('renders correctly', () => {
    render(<Component />);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
```

## 🔧 配置

### 环境变量
复制 `.env.example` 为 `.env.local` 并配置：

```env
# Google Analytics
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# API配置
VITE_API_BASE_URL=https://api.disc-traits.com

# 功能开关
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PWA=true
```

### 构建配置
- **Vite配置** - `vite.config.ts`
- **TypeScript配置** - `tsconfig.json`
- **Tailwind配置** - `tailwind.config.js`
- **测试配置** - `vitest.config.ts`

## 📊 性能优化

### 构建优化
- 代码分割和懒加载
- Tree shaking
- 资源压缩和优化
- Bundle分析

### 运行时优化
- React.memo和useMemo
- 虚拟滚动
- 图片懒加载
- Service Worker缓存

### 监控指标
- Core Web Vitals
- 错误率监控
- 用户行为分析
- 性能预算

## 🔒 安全性

### 安全措施
- Content Security Policy (CSP)
- XSS防护
- CSRF防护
- 数据加密
- 输入验证

### 隐私保护
- 数据最小化收集
- 用户同意管理
- 数据匿名化
- GDPR合规

## 🌍 国际化

### 支持语言
- 中文 (zh)
- English (en)
- Español (es)

### 添加新语言
1. 在 `src/data/translations/` 添加语言文件
2. 更新 `src/types/index.ts` 中的语言类型
3. 更新 `src/contexts/LanguageContext.tsx`

## 📱 PWA功能

### 特性
- 离线访问
- 应用安装
- 推送通知
- 后台同步

### 配置
- `public/manifest.json` - PWA清单
- `public/sw.js` - Service Worker
- 图标和启动画面

## 🤝 贡献指南

### 开发流程
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试
chore: 构建/工具
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🆘 支持

### 问题反馈
- [GitHub Issues](https://github.com/disc-traits/disc-traits-web/issues)
- 邮箱: <EMAIL>

### 文档
- [用户指南](docs/user-guide.md)
- [API文档](docs/api.md)
- [部署指南](docs/deployment.md)

## 🎯 路线图

### v1.1 (计划中)
- [ ] 团队评估功能
- [ ] 高级报告模板
- [ ] 数据导出功能

### v1.2 (计划中)
- [ ] 管理员后台
- [ ] 用户账户系统
- [ ] 评估历史记录

---

**DISC Traits** - 帮助个人和团队更好地理解行为风格，改善沟通和协作。
