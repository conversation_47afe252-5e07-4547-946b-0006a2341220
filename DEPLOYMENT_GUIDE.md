# DISC Traits 部署指南

## 🚀 快速部署

### 1. 环境准备
```bash
# 确保已安装 Node.js 18+
node --version

# 确保已安装 npm 9+
npm --version
```

### 2. 项目设置
```bash
# 安装依赖
npm install

# 复制环境变量配置
cp .env.example .env.local

# 编辑环境变量（可选）
# 主要配置 Google Analytics ID
```

### 3. 开发环境
```bash
# 启动开发服务器
npm run dev

# 访问 http://localhost:5174
```

### 4. 生产构建
```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 🔧 环境变量配置

### 必需配置
```env
# Google Analytics（推荐配置）
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

### 可选配置
```env
# API配置
VITE_API_BASE_URL=https://api.disc-traits.com

# 功能开关
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PWA=true
VITE_ENABLE_A11Y_FEATURES=true

# 联系信息
VITE_CONTACT_EMAIL=<EMAIL>
VITE_FEEDBACK_EMAIL=<EMAIL>
```

## 📦 部署选项

### 选项1: 静态托管（推荐）
适用于 Vercel、Netlify、GitHub Pages 等

```bash
# 构建
npm run build

# 部署 dist/ 目录到静态托管服务
```

### 选项2: 服务器部署
```bash
# 使用部署脚本
npm run deploy:production

# 或手动部署
npm run build
rsync -avz --delete dist/ user@server:/var/www/disc-traits/
```

### 选项3: Docker 部署
```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🌐 域名和SSL配置

### Nginx 配置示例
```nginx
server {
    listen 443 ssl http2;
    server_name disc-traits.com www.disc-traits.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    root /var/www/disc-traits;
    index index.html;
    
    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 静态资源缓存
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Service Worker
    location /sw.js {
        expires 0;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # 安全头
    add_header X-Frame-Options "DENY";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
}
```

## 📊 监控和分析

### Google Analytics 设置
1. 创建 GA4 属性
2. 获取测量ID (G-XXXXXXXXXX)
3. 在 .env.local 中配置 VITE_GA_MEASUREMENT_ID

### 性能监控
- Core Web Vitals 自动追踪
- 错误监控和报告
- 用户行为分析

## 🔒 安全配置

### 内容安全策略 (CSP)
已在 SecurityEnhancer 组件中配置：
- 防止 XSS 攻击
- 限制资源加载来源
- 防止点击劫持

### HTTPS 强制
```nginx
# 重定向 HTTP 到 HTTPS
server {
    listen 80;
    server_name disc-traits.com www.disc-traits.com;
    return 301 https://$server_name$request_uri;
}
```

## 🎯 SEO 优化

### 已实现的SEO功能
- 结构化数据 (JSON-LD)
- 多语言 hreflang 标签
- 优化的 meta 标签
- 自动生成的 sitemap.xml
- 搜索引擎友好的 URL

### 提交到搜索引擎
```bash
# Google Search Console
# 提交 sitemap: https://disc-traits.com/sitemap.xml

# Bing Webmaster Tools
# 提交 sitemap: https://disc-traits.com/sitemap.xml
```

## 🔄 更新和维护

### 定期更新
```bash
# 更新依赖
npm update

# 检查安全漏洞
npm audit

# 修复安全问题
npm audit fix
```

### 备份策略
- 定期备份源代码
- 备份用户数据（如果有）
- 备份配置文件

## 📱 PWA 配置

### Service Worker
- 自动缓存静态资源
- 离线访问支持
- 后台同步

### 应用安装
用户可以将网站安装为原生应用：
- 桌面：Chrome 地址栏的安装图标
- 移动端：浏览器菜单中的"添加到主屏幕"

## 🐛 故障排除

### 常见问题

**构建失败**
```bash
# 清理缓存
npm run clean
npm install
npm run build
```

**路由不工作**
- 确保服务器配置了 SPA 路由支持
- 检查 try_files 配置

**PWA 不工作**
- 确保在 HTTPS 环境下
- 检查 Service Worker 注册
- 验证 manifest.json

### 日志查看
```bash
# 开发环境
npm run dev
# 查看浏览器控制台

# 生产环境
# 查看服务器日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

## 📞 技术支持

如有问题，请联系：
- 邮箱: <EMAIL>
- GitHub Issues: [项目仓库]/issues

## 📈 性能优化建议

### 已实现的优化
- 代码分割和懒加载
- 图片优化和懒加载
- Gzip 压缩
- 浏览器缓存策略
- CDN 支持

### 进一步优化
- 配置 CDN
- 启用 HTTP/2
- 优化图片格式 (WebP)
- 实施缓存策略

---

**部署成功后，您的 DISC Traits 网站将提供完整的人格评估服务！**
